# Multi-Model Training Pipeline for RGB and IR Image Segmentation

This repository provides a comprehensive training pipeline for 5 different deep learning models using both RGB and IR image datasets.

## 🎯 Overview

The pipeline trains the following models:
1. **UNet** - Standard U-Net architecture
2. **ResUNet** - Residual U-Net with skip connections
3. **CSWinUNet** - Cross-Shaped Window Transformer U-Net
4. **CSWinUNet-CBAM** - CSWin-UNet with Convolutional Block Attention Module
5. **TransUNet** - Transformer-based U-Net (TensorFlow implementation - currently skipped)

Each model is trained on:
- **RGB images** (3-channel visible light images)
- **IR images** (1-channel infrared images)

## 📁 Directory Structure

```
CSWin-CBAM/
├── data-if/                          # Dataset directory
│   ├── 01-Visible images/           # RGB images
│   ├── 02-Infrared images/          # IR images
│   └── 04-Ground truth/             # Segmentation masks
├── layer/                           # Model architectures
│   ├── UNet.py
│   ├── ResUNet.py
│   ├── CSWinUnet.py
│   ├── CSWinUnet-CBAM.py
│   └── TransUnet.py
├── multi_model_trainer.py           # Main training pipeline
├── train_all_models.py             # Training script
├── test_models.py                   # Model testing utility
├── optimal_dataset.py               # Dataset handlers
├── loss_functons.py                 # Loss functions and metrics
└── README_MultiModel.md             # This file
```

## 🚀 Quick Start

### 1. Test the Setup
First, verify that all models and datasets work correctly:

```bash
python test_models.py
```

This will:
- Test model creation with different input channels
- Verify dataset loading
- Check training compatibility

### 2. Run Quick Test
Run a quick test with reduced epochs:

```bash
python train_all_models.py --quick_test
```

### 3. Full Training
Run comprehensive training for all models:

```bash
python train_all_models.py
```

### 4. Custom Training
Customize training parameters:

```bash
python train_all_models.py \
    --epochs 100 \
    --batch_size 16 \
    --lr 0.0005 \
    --data_dir ./data-if \
    --results_dir my_results
```

## 📊 Results and Outputs

After training, the following files will be generated in the results directory:

### Summary Files
- `comprehensive_summary.json` - Complete results in JSON format
- `training_report.txt` - Human-readable performance report

### Visualizations
- `model_comparison.png` - Performance comparison charts
- `training_curves.png` - Training progress for all models

### Model Weights
- `best_UNet_rgb.pth` - Best UNet model for RGB data
- `best_UNet_ir.pth` - Best UNet model for IR data
- `best_ResUNet_rgb.pth` - Best ResUNet model for RGB data
- ... (and so on for all models)

### Training Histories
- `history_UNet_rgb.json` - Training metrics for UNet RGB
- `history_UNet_ir.json` - Training metrics for UNet IR
- ... (and so on for all models)

## 🔧 Configuration Options

### Command Line Arguments

| Argument | Default | Description |
|----------|---------|-------------|
| `--data_dir` | `./data-if` | Path to dataset directory |
| `--results_dir` | `multi_model_results` | Directory to save results |
| `--epochs` | `50` | Number of training epochs per model |
| `--batch_size` | `8` | Batch size for training |
| `--lr` | `0.001` | Learning rate |
| `--quick_test` | `False` | Run with 5 epochs for quick testing |

### Model Input Configurations

Each model automatically adapts to the appropriate input channels:
- **RGB data**: 3 channels (Red, Green, Blue)
- **IR data**: 1 channel (Infrared)
- **Combined**: 4 channels (RGB + IR) - for advanced models

## 📈 Performance Metrics

The pipeline tracks the following metrics:
- **IoU (Intersection over Union)** - Primary segmentation metric
- **Training Loss** - Combined loss (CrossEntropy + Dice + IoU)
- **Validation Loss** - Loss on validation set
- **Training Curves** - Progress over epochs

## 🛠️ Technical Details

### Dataset Handling
- Automatic image resizing to 224x224
- Normalization to [0, 1] range
- Binary segmentation masks (threshold at 128)
- 80/20 train/validation split

### Loss Function
Combined loss with weighted components:
- 30% Cross-Entropy Loss
- 40% Dice Loss
- 30% IoU Loss

### Optimization
- **Optimizer**: AdamW with weight decay (1e-4)
- **Scheduler**: Cosine Annealing Learning Rate
- **Early Stopping**: Based on validation IoU

## 🔍 Model Comparison

The pipeline automatically generates comparison visualizations showing:
1. **Side-by-side IoU comparison** for RGB vs IR
2. **Performance heatmap** across all models
3. **Training curves** for each model
4. **Best performing models** summary

## 🐛 Troubleshooting

### Common Issues

1. **CUDA out of memory**
   ```bash
   python train_all_models.py --batch_size 4
   ```

2. **Dataset not found**
   - Ensure `data-if` directory exists
   - Check subdirectories: `01-Visible images`, `02-Infrared images`, `04-Ground truth`

3. **Model import errors**
   - Run `python test_models.py` to diagnose issues
   - Check that all model files are present in `layer/` directory

### Performance Tips

1. **Use GPU**: Ensure CUDA is available for faster training
2. **Adjust batch size**: Reduce if running out of memory
3. **Monitor progress**: Check training curves for convergence
4. **Early stopping**: Models stop when target IoU is reached

## 📝 Example Output

```
================================================================================
COMPREHENSIVE MULTI-MODEL TRAINING
================================================================================
Models: ['UNet', 'ResUNet', 'CSWinUNet', 'CSWinUNet_CBAM']
Image types: ['rgb', 'ir']
Epochs per model: 50
Batch size: 8
Learning rate: 0.001
================================================================================

Training UNet with RGB data...
Found 150 valid RGB samples
Epoch 1: Train Loss=0.4523, Val Loss=0.3876, Val IoU=0.7234
...
✓ UNet (RGB): Best IoU = 0.8456

Training UNet with IR data...
Found 150 valid IR samples
...
✓ UNet (IR): Best IoU = 0.8123

...

🎉 TRAINING COMPLETED SUCCESSFULLY!
📊 FINAL RESULTS SUMMARY:
🟢 UNet           (RGB): IoU = 0.8456
🟢 UNet           (IR ): IoU = 0.8123
🟢 ResUNet        (RGB): IoU = 0.8234
🟡 ResUNet        (IR ): IoU = 0.7891
...
🏆 Best Overall: CSWinUNet_CBAM (RGB) with IoU = 0.8789
```

## 🤝 Contributing

To add new models:
1. Create model file in `layer/` directory
2. Add model to `ModelAdapter.get_model()` method
3. Update `available_models` list in `MultiModelTrainer`
4. Test with `python test_models.py`

## 📄 License

This project is part of the CSWin-CBAM research implementation.
