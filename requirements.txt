# Core deep learning
torch>=1.9.0
torchvision>=0.10.0
numpy>=1.21.0

# Image processing
opencv-python>=4.5.0
Pillow>=8.3.0

# Data handling
matplotlib>=3.4.0
tqdm>=4.62.0

# Scientific computing
scipy>=1.7.0

# Optional: For better performance
# tensorboard>=2.7.0  # For logging (optional)
# albumentations>=1.1.0  # For advanced augmentations (optional)

# For TransUNet (if implementing PyTorch version)
# timm>=0.4.12  # For vision transformers
# einops>=0.3.2  # For tensor operations
