import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from einops import rearrange
from einops.layers.torch import Rearrange

# ======================== CBAM MODULES ========================

class ChannelAttention(nn.Module):
    def __init__(self, in_planes, ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
           
        self.fc = nn.Sequential(nn.Conv2d(in_planes, in_planes // ratio, 1, bias=False),
                               nn.ReLU(),
                               nn.Conv2d(in_planes // ratio, in_planes, 1, bias=False))
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()

        self.conv1 = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv1(x)
        return self.sigmoid(x)

class CBAM(nn.Module):
    def __init__(self, in_planes, ratio=16, kernel_size=7):
        super(CBAM, self).__init__()
        self.ca = ChannelAttention(in_planes, ratio)
        self.sa = SpatialAttention(kernel_size)

    def forward(self, x):
        x = x * self.ca(x)
        x = x * self.sa(x)
        return x

# ======================== CSWIN COMPONENTS ========================

class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x

class LePEAttention(nn.Module):
    def __init__(self, dim, resolution, idx, split_size=7, dim_out=None, num_heads=8, attn_drop=0., proj_drop=0., qk_scale=None):
        super().__init__()
        self.dim = dim
        self.dim_out = dim_out or dim
        self.resolution = resolution
        self.split_size = split_size
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = qk_scale or head_dim ** -0.5
        self.idx = idx
        
        if idx == -1:
            H_sp, W_sp = self.resolution, self.resolution
        elif idx == 0:
            H_sp, W_sp = self.resolution, self.split_size
        elif idx == 1:
            W_sp, H_sp = self.resolution, self.split_size
        else:
            print("ERROR MODE", idx)
            exit(0)
        self.H_sp = H_sp
        self.W_sp = W_sp
        self.get_v = nn.Conv2d(dim, dim, kernel_size=3, stride=1, padding=1, groups=dim)
        self.attn_drop = nn.Dropout(attn_drop)

    def im2cswin(self, x):
        B, N, C = x.shape
        H = W = self.resolution
        x = x.transpose(-2,-1).contiguous().view(B, C, H, W)
        x = img2windows(x, self.H_sp, self.W_sp)
        x = x.reshape(-1, self.H_sp* self.W_sp, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3).contiguous()
        return x

    def get_lepe(self, x, func):
        B, N, C = x.shape
        H = W = self.resolution
        x = x.transpose(-2,-1).contiguous().view(B, C, H, W)

        H_sp, W_sp = self.H_sp, self.W_sp
        x = x.view(B, C, H // H_sp, H_sp, W // W_sp, W_sp)
        x = x.permute(0, 2, 4, 1, 3, 5).contiguous().reshape(-1, C, H_sp, W_sp)

        lepe = func(x)
        lepe = lepe.reshape(-1, self.num_heads, C // self.num_heads, H_sp * W_sp).permute(0, 1, 3, 2).contiguous()

        x = x.reshape(-1, self.num_heads, C // self.num_heads, self.H_sp* self.W_sp).permute(0, 1, 3, 2).contiguous()
        return x, lepe

    def forward(self, qkv):
        q, k, v = qkv[0], qkv[1], qkv[2]

        H = W = self.resolution
        B, L, C = q.shape
        assert L == H * W, "flatten img_tokens has wrong size"

        q = self.im2cswin(q)
        k = self.im2cswin(k)
        v, lepe = self.get_lepe(v, self.get_v)

        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))
        attn = nn.functional.softmax(attn, dim=-1, dtype=attn.dtype)
        attn = self.attn_drop(attn)

        x = (attn @ v) + lepe
        x = x.transpose(1, 2).reshape(-1, self.H_sp * self.W_sp, C)

        x = windows2img(x, self.H_sp, self.W_sp, H, W).view(B, -1, C)
        return x

class CSWinBlock(nn.Module):
    def __init__(self, dim, reso, num_heads, split_size, mlp_ratio=4., qkv_bias=False, qk_scale=None,
                 drop=0., attn_drop=0., drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm, 
                 last_stage=False, use_cbam=True):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.patches_resolution = reso
        self.split_size = split_size
        self.mlp_ratio = mlp_ratio
        self.use_cbam = use_cbam
        
        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.norm1 = norm_layer(dim)

        if self.patches_resolution == split_size:
            last_stage = True
        if last_stage:
            self.branch_num = 1
        else:
            self.branch_num = 2

        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(drop)

        if last_stage:
            self.attns = nn.ModuleList([
                LePEAttention(
                    dim//2, resolution=self.patches_resolution, idx = -1,
                    split_size=split_size, num_heads=num_heads//2, dim_out=dim//2,
                    qk_scale=qk_scale, attn_drop=attn_drop, proj_drop=drop)
                for i in range(self.branch_num)])
        else:
            self.attns = nn.ModuleList([
                LePEAttention(
                    dim//2, resolution=self.patches_resolution, idx = i,
                    split_size=split_size, num_heads=num_heads//2, dim_out=dim//2,
                    qk_scale=qk_scale, attn_drop=attn_drop, proj_drop=drop)
                for i in range(self.branch_num)])

        mlp_hidden_dim = int(dim * mlp_ratio)

        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)
        self.norm2 = norm_layer(dim)
        
        # Add CBAM attention
        if self.use_cbam:
            self.cbam = CBAM(dim)

    def forward(self, x):
        H = W = self.patches_resolution
        B, L, C = x.shape
        assert L == H * W, "flatten img_tokens has wrong size"
        
        # Apply CBAM if enabled
        if self.use_cbam:
            # Convert to spatial format for CBAM
            x_spatial = x.transpose(-2, -1).contiguous().view(B, C, H, W)
            x_spatial = self.cbam(x_spatial)
            x = x_spatial.view(B, C, -1).transpose(-2, -1).contiguous()
        
        img = self.norm1(x)
        qkv = self.qkv(img).reshape(B, -1, 3, C).permute(2, 0, 1, 3)

        if self.branch_num == 2:
            x1 = self.attns[0](qkv[:, :, :, :C // 2])
            x2 = self.attns[1](qkv[:, :, :, C // 2:])
            attened_x = torch.cat([x1, x2], dim=2)
        else:
            attened_x = self.attns[0](qkv)
            
        attened_x = self.proj(attened_x)
        x = x + self.drop_path(attened_x)
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        return x

class Merge_Block(nn.Module):
    def __init__(self, dim, dim_out, norm_layer=nn.LayerNorm, use_cbam=True):
        super().__init__()
        self.conv = nn.Conv2d(dim, dim_out, 3, 2, 1)
        self.norm = norm_layer(dim_out)
        self.use_cbam = use_cbam
        if self.use_cbam:
            self.cbam = CBAM(dim_out)

    def forward(self, x):
        B, new_HW, C = x.shape
        H = W = int(np.sqrt(new_HW))
        x = x.transpose(-2, -1).contiguous().view(B, C, H, W)
        x = self.conv(x)
        
        if self.use_cbam:
            x = self.cbam(x)
            
        B, C = x.shape[:2]
        x = x.view(B, C, -1).transpose(-2, -1).contiguous()
        x = self.norm(x)
        return x

class CARAFE_CBAM(nn.Module):
    def __init__(self, c, c_mid=64, scale=2, k_up=5, k_enc=3, use_cbam=True):
        super(CARAFE_CBAM, self).__init__()
        self.scale = scale
        self.k_up = k_up
        self.k_enc = k_enc
        self.c = c
        self.c_mid = c_mid
        self.use_cbam = use_cbam

        self.encoder = nn.Sequential(
            nn.Conv2d(c, self.c_mid, k_enc, padding=k_enc//2),
            nn.BatchNorm2d(self.c_mid),
            nn.ReLU(inplace=True),
            nn.Conv2d(self.c_mid, self.scale*self.scale*self.k_up*self.k_up, k_enc, padding=k_enc//2),
            nn.PixelShuffle(self.scale)
        )
        
        if self.use_cbam:
            self.cbam = CBAM(c)

    def forward(self, x):
        B, C, H, W = x.shape
        
        # Apply CBAM before upsampling
        if self.use_cbam:
            x = self.cbam(x)
            
        kernel = self.encoder(x)
        kernel = F.softmax(kernel, dim=1)
        
        x = F.unfold(x, self.k_up, padding=self.k_up//2)
        x = x.view(B, C, self.k_up*self.k_up, H, W)
        x = x.permute(0, 3, 4, 1, 2).contiguous()
        
        kernel = kernel.permute(0, 2, 3, 1).contiguous().view(B, H, W, self.k_up*self.k_up, 1)
        x = torch.matmul(x, kernel).squeeze(-1)
        x = x.permute(0, 3, 1, 2).contiguous()
        
        return F.interpolate(x, scale_factor=self.scale, mode='nearest')

# ======================== UTILITY FUNCTIONS ========================

def img2windows(img, H_sp, W_sp):
    B, C, H, W = img.shape
    img_reshape = img.view(B, C, H // H_sp, H_sp, W // W_sp, W_sp)
    img_perm = img_reshape.permute(0, 2, 4, 3, 5, 1).contiguous().reshape(-1, H_sp * W_sp, C)
    return img_perm

def windows2img(img_splits_hw, H_sp, W_sp, H, W):
    B = int(img_splits_hw.shape[0] / (H * W / H_sp / W_sp))
    img = img_splits_hw.view(B, H // H_sp, W // W_sp, H_sp, W_sp, -1)
    img = img.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
    return img

class DropPath(nn.Module):
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()
        output = x.div(keep_prob) * random_tensor
        return output

# ======================== MAIN MODEL ========================

class CSWinTransformer_CBAM(nn.Module):
    def __init__(self, img_size=224, patch_size=16, in_chans=3, num_classes=8, embed_dim=64, 
                 depth=[1, 2, 9, 1], split_size=[1, 2, 7, 7], num_heads=12, mlp_ratio=4., 
                 qkv_bias=True, qk_scale=None, drop_rate=0., attn_drop_rate=0., drop_path_rate=0, 
                 norm_layer=nn.LayerNorm, use_chk=False, use_cbam=True):
        super().__init__()
        self.use_chk = use_chk
        self.use_cbam = use_cbam
        self.num_classes = num_classes
        self.num_features = self.embed_dim = embed_dim
        heads = num_heads

        # Encoder with CBAM
        self.stage1_conv_embed = nn.Sequential(
            nn.Conv2d(in_chans, embed_dim, 7, 4, 2),
            CBAM(embed_dim) if use_cbam else nn.Identity(),
            Rearrange('b c h w -> b (h w) c', h=img_size // 4, w=img_size // 4),
            nn.LayerNorm(embed_dim)
        )

        curr_dim = embed_dim
        self.pos_drop = nn.Dropout(p=drop_rate)
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, np.sum(depth))]

        # Encoder stages with CBAM
        self.stage1 = nn.ModuleList([
            CSWinBlock(dim=curr_dim, num_heads=heads[0], reso=img_size // 4, mlp_ratio=mlp_ratio,
                      qkv_bias=qkv_bias, qk_scale=qk_scale, split_size=split_size[0],
                      drop=drop_rate, attn_drop=attn_drop_rate, drop_path=dpr[i], 
                      norm_layer=norm_layer, use_cbam=use_cbam)
            for i in range(depth[0])])
        
        self.merge1 = Merge_Block(curr_dim, curr_dim * 2, use_cbam=use_cbam)
        curr_dim = curr_dim * 2
        
        self.stage2 = nn.ModuleList([
            CSWinBlock(dim=curr_dim, num_heads=heads[1], reso=img_size // 8, mlp_ratio=mlp_ratio,
                      qkv_bias=qkv_bias, qk_scale=qk_scale, split_size=split_size[1],
                      drop=drop_rate, attn_drop=attn_drop_rate, drop_path=dpr[np.sum(depth[:1]) + i], 
                      norm_layer=norm_layer, use_cbam=use_cbam)
            for i in range(depth[1])])
        
        self.merge2 = Merge_Block(curr_dim, curr_dim * 2, use_cbam=use_cbam)
        curr_dim = curr_dim * 2
        
        self.stage3 = nn.ModuleList([
            CSWinBlock(dim=curr_dim, num_heads=heads[2], reso=img_size // 16, mlp_ratio=mlp_ratio,
                      qkv_bias=qkv_bias, qk_scale=qk_scale, split_size=split_size[2],
                      drop=drop_rate, attn_drop=attn_drop_rate, drop_path=dpr[np.sum(depth[:2]) + i], 
                      norm_layer=norm_layer, use_cbam=use_cbam)
            for i in range(depth[2])])
        
        self.merge3 = Merge_Block(curr_dim, curr_dim * 2, use_cbam=use_cbam)
        curr_dim = curr_dim * 2
        
        self.stage4 = nn.ModuleList([
            CSWinBlock(dim=curr_dim, num_heads=heads[3], reso=img_size // 32, mlp_ratio=mlp_ratio,
                      qkv_bias=qkv_bias, qk_scale=qk_scale, split_size=split_size[-1],
                      drop=drop_rate, attn_drop=attn_drop_rate, drop_path=dpr[np.sum(depth[:-1]) + i], 
                      norm_layer=norm_layer, last_stage=True, use_cbam=use_cbam)
            for i in range(depth[-1])])

        self.norm = norm_layer(curr_dim)

        # Decoder with CBAM
        self.stage_up4 = nn.ModuleList([
            CSWinBlock(dim=curr_dim, num_heads=heads[3], reso=img_size // 32, mlp_ratio=mlp_ratio,
                      qkv_bias=qkv_bias, qk_scale=qk_scale, split_size=split_size[-1],
                      drop=drop_rate, attn_drop=attn_drop_rate, drop_path=dpr[np.sum(depth[:-1]) + i], 
                      norm_layer=norm_layer, last_stage=True, use_cbam=use_cbam)
            for i in range(depth[-1])])

        self.upsample4 = CARAFE_CBAM(curr_dim, curr_dim // 2, use_cbam=use_cbam)
        curr_dim = curr_dim // 2

        self.concat_linear4 = nn.Linear(512, 256)
        self.stage_up3 = nn.ModuleList([
            CSWinBlock(dim=curr_dim, num_heads=heads[2], reso=img_size // 16, mlp_ratio=mlp_ratio,
                      qkv_bias=qkv_bias, qk_scale=qk_scale, split_size=split_size[2],
                      drop=drop_rate, attn_drop=attn_drop_rate, drop_path=dpr[np.sum(depth[:2]) + i], 
                      norm_layer=norm_layer, use_cbam=use_cbam)
            for i in range(depth[2])])

        self.upsample3 = CARAFE_CBAM(curr_dim, curr_dim // 2, use_cbam=use_cbam)
        curr_dim = curr_dim // 2

        self.concat_linear3 = nn.Linear(256, 128)
        self.stage_up2 = nn.ModuleList([
            CSWinBlock(dim=curr_dim, num_heads=heads[1], reso=img_size // 8, mlp_ratio=mlp_ratio,
                      qkv_bias=qkv_bias, qk_scale=qk_scale, split_size=split_size[1],
                      drop=drop_rate, attn_drop=attn_drop_rate, drop_path=dpr[np.sum(depth[:1]) + i], 
                      norm_layer=norm_layer, use_cbam=use_cbam)
            for i in range(depth[1])])
        
        self.upsample2 = CARAFE_CBAM(curr_dim, curr_dim // 2, use_cbam=use_cbam)
        curr_dim = curr_dim // 2

        self.concat_linear2 = nn.Linear(128, 64)
        self.stage_up1 = nn.ModuleList([
            CSWinBlock(dim=curr_dim, num_heads=heads[0], reso=img_size // 4, mlp_ratio=mlp_ratio,
                      qkv_bias=qkv_bias, qk_scale=qk_scale, split_size=split_size[0],
                      drop=drop_rate, attn_drop=attn_drop_rate, drop_path=dpr[i], 
                      norm_layer=norm_layer, use_cbam=use_cbam)
            for i in range(depth[0])])

        self.upsample1 = CARAFE_CBAM(curr_dim, 64, scale=4, use_cbam=use_cbam)
        self.norm_up = norm_layer(embed_dim)
        
        # Final output with CBAM
        self.output_cbam = CBAM(embed_dim) if use_cbam else nn.Identity()
        self.output = nn.Conv2d(embed_dim, self.num_classes, kernel_size=1, bias=False)

    def forward_features(self, x):
        x = self.stage1_conv_embed(x)
        x = self.pos_drop(x)

        for blk in self.stage1:
            x = blk(x)
        self.x1 = x
        x = self.merge1(x)

        for blk in self.stage2:
            x = blk(x)
        self.x2 = x
        x = self.merge2(x)

        for blk in self.stage3:
            x = blk(x)
        self.x3 = x
        x = self.merge3(x)

        for blk in self.stage4:
            x = blk(x)

        x = self.norm(x)
        return x

    def forward_up_features(self, x):
        for blk in self.stage_up4:
            x = blk(x)
        x = self.upsample4(x)
        x = torch.cat([self.x3, x], -1)
        x = self.concat_linear4(x)
        
        for blk in self.stage_up3:
            x = blk(x)
        x = self.upsample3(x)
        x = torch.cat([self.x2, x], -1)
        x = self.concat_linear3(x)
        
        for blk in self.stage_up2:
            x = blk(x)
        x = self.upsample2(x)
        x = torch.cat([self.x1, x], -1)
        x = self.concat_linear2(x)
        
        for blk in self.stage_up1:
            x = blk(x)
        x = self.norm_up(x)
        return x

    def up_x4(self, x):
        B, new_HW, C = x.shape
        H = W = int(np.sqrt(new_HW))
        x = self.upsample1(x)
        x = x.view(B, 4 * H, 4 * W, -1)
        x = x.permute(0, 3, 1, 2)
        x = self.output_cbam(x)
        x = self.output(x)
        return x

    def forward(self, x):
        x = self.forward_features(x)
        x = self.forward_up_features(x)
        x = self.up_x4(x)
        return x

# Usage:
"""
# CSWin-UNet with CBAM
model = CSWinTransformer_CBAM(
    img_size=224,
    in_chans=3,
    num_classes=8,
    embed_dim=64,
    depth=[1, 2, 9, 1],
    split_size=[1, 2, 7, 7],
    num_heads=[2, 4, 8, 16],
    use_cbam=True
)

# Without CBAM (standard CSWin-UNet)
model = CSWinTransformer_CBAM(use_cbam=False)

# Forward pass
output = model(input_tensor)  # [B, C, H, W] -> [B, num_classes, H, W]
"""