import argparse
from optimal_cswin_trainer import train_optimal_cswin
from optimal_cswin_trainer import OptimalCSWinUNet
import torch



def main():
    parser = argparse.ArgumentParser(description='Optimal CSWin-UNet Training')
    parser.add_argument('--data_dir', type=str, default='./data-if', help='Data directory')
    parser.add_argument('--image_type', type=str, choices=['rgb', 'ir'], default='rgb', help='Image type')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--target_iou', type=float, default=0.85, help='Target IoU')
    parser.add_argument('--no_early_stop', action='store_true', help='Disable early stopping')

    args = parser.parse_args()

    print(" Optimal CSWin-UNet Training")
    print("=" * 60)
    print(f"Dataset: {args.image_type.upper()}")
    print(f"Target IoU: {args.target_iou}")
    print(f"Epochs: {args.epochs}")
    print(f"Batch Size: {args.batch_size}")
    print(f"Learning Rate: {args.lr}")
    print(f"Early Stopping: {'Disabled' if args.no_early_stop else 'Enabled'}")
    print("=" * 60)

    # Train model
    best_iou = train_optimal_cswin(
        data_dir=args.data_dir,
        image_type=args.image_type,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr,
        target_iou=args.target_iou,
        no_early_stop=args.no_early_stop
    )

    print(f"\n TRAINING COMPLETED!")
    print("=" * 60)
    print(f"Best IoU achieved: {best_iou:.6f}")

    if best_iou >= args.target_iou:
        print(f" SUCCESS! Target IoU {args.target_iou} achieved!")
    else:
        print(f"  Target IoU {args.target_iou} not reached.")

    print(f"\n COMPREHENSIVE RESULTS GENERATED:")
    print(f"   Model weights: best_optimal_cswin_{args.image_type}.pth")
    print(f"  Training curves: comprehensive_training_curves_{args.image_type}.png")
    print(f"   Simple curves: simple_training_curves_{args.image_type}.png")
    print(f"   Metrics report: metrics_report_{args.image_type}.txt")
    print(f"    All predictions: predictions_{args.image_type}_all/")
    print(f"   Prediction gallery: sample_gallery_{args.image_type}.png")
    print(f"   Checkpoints: checkpoint_epoch_*_{args.image_type}.pth")
    print("=" * 60)

def run_comparison_training():
    """Run training for both RGB and IR datasets and compare results"""
    print(" Running comparison training for both RGB and IR datasets...")

    results = {}

    for image_type in ['rgb', 'ir']:
        print(f"\n{'='*20} {image_type.upper()} TRAINING {'='*20}")

        best_iou = train_optimal_cswin(
            data_dir='./data-if',
            image_type=image_type,
            epochs=50,  # Reduced for comparison
            batch_size=8,
            lr=0.001,
            target_iou=0.85
        )

        results[image_type] = best_iou
        print(f"{image_type.upper()} Best IoU: {best_iou:.4f}")

    # Generate comparison report
    print("\n" + "="*50)
    print(" COMPARISON RESULTS")
    print("="*50)

    for image_type, iou in results.items():
        status = " TARGET ACHIEVED" if iou >= 0.85 else "  NEEDS IMPROVEMENT"
        print(f"{image_type.upper():>3}: IoU = {iou:.4f} - {status}")

    # Create comparison visualization
    create_comparison_plot(results)

    return results

def create_comparison_plot(results):
    """Create comparison plot between RGB and IR results"""
    import matplotlib.pyplot as plt

    datasets = list(results.keys())
    ious = list(results.values())

    plt.figure(figsize=(10, 6))
    bars = plt.bar(datasets, ious, color=['blue', 'red'], alpha=0.7)
    plt.axhline(y=0.85, color='green', linestyle='--', linewidth=2, label='Target IoU (0.85)')

    # Add value labels on bars
    for bar, iou in zip(bars, ious):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{iou:.4f}', ha='center', va='bottom', fontweight='bold')

    plt.ylim(0, 1.0)
    plt.ylabel('IoU Score')
    plt.title('Optimal CSWin-UNet: RGB vs IR Performance Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Color bars based on target achievement
    for i, (bar, iou) in enumerate(zip(bars, ious)):
        if iou >= 0.85:
            bar.set_color('green')
            bar.set_alpha(0.8)

    plt.tight_layout()
    plt.savefig('comparison_results.png', dpi=150, bbox_inches='tight')
    plt.close()

    print(" Comparison plot saved as 'comparison_results.png'")

def test_model_inference():
    """Test trained model inference speed and accuracy"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Load model
    model = OptimalCSWinUNet(num_classes=2).to(device)

    # Test with dummy data
    dummy_input = torch.randn(1, 4, 224, 224).to(device)

    model.eval()
    with torch.no_grad():
        import time

        # Warmup
        for _ in range(10):
            _ = model(dummy_input)

        # Measure inference time
        start_time = time.time()
        for _ in range(100):
            output = model(dummy_input)
        end_time = time.time()

        avg_inference_time = (end_time - start_time) / 100
        fps = 1.0 / avg_inference_time

        print(f"  Model Performance:")
        print(f"  - Average inference time: {avg_inference_time*1000:.2f} ms")
        print(f"  - FPS: {fps:.2f}")
        print(f"  - Output shape: {output.shape}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == '--compare':
        # Run comparison mode
        run_comparison_training()
    elif len(sys.argv) > 1 and sys.argv[1] == '--test':
        # Test inference
        test_model_inference()
    else:
        # Run normal training
        main()
