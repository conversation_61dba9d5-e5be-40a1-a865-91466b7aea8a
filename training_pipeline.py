import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from tqdm import tqdm
import matplotlib.pyplot as plt
import numpy as np
from utils import calculate_iou
from OptimalDataset import OptimalDataset
from networks.CSWinUnet import OptimalCSWinUNet



def train_optimal_cswin(data_dir, image_type='rgb', epochs=100, batch_size=8, lr=0.001, target_iou=0.85, no_early_stop=False):
    """Complete training pipeline for Optimal CSWin-UNet with comprehensive reporting"""

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Create dataset
    dataset = OptimalDataset(data_dir, image_type=image_type)

    # Split dataset
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

    # Create model
    model = OptimalCSWinUNet(num_classes=2).to(device)

    # Loss and optimizer
    criterion = CombinedLoss(num_classes=2)
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)

    # Training tracking
    best_iou = 0.0
    train_losses, val_losses, val_ious = [], [], []
    train_ious = []  # Track training IoU too
    learning_rates = []

    print(f"Starting training for {image_type.upper()} dataset...")
    print(f"Target IoU: {target_iou}")
    print(f"Total epochs: {epochs}")
    print(f"Early stopping: {'Disabled' if no_early_stop else 'Enabled'}")

    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0.0
        train_iou = 0.0

        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}"):
            images = batch['image'].to(device)
            labels = batch['label'].to(device)

            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()
            train_iou += calculate_iou(outputs, labels)

        # Validation
        model.eval()
        val_loss = 0.0
        val_iou = 0.0

        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(device)
                labels = batch['label'].to(device)

                outputs = model(images)
                loss = criterion(outputs, labels)
                iou = calculate_iou(outputs, labels)

                val_loss += loss.item()
                val_iou += iou

        # Calculate averages
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        avg_train_iou = train_iou / len(train_loader)
        avg_val_iou = val_iou / len(val_loader)
        current_lr = optimizer.param_groups[0]['lr']

        # Store metrics
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        train_ious.append(avg_train_iou)
        val_ious.append(avg_val_iou)
        learning_rates.append(current_lr)

        scheduler.step()

        print(f"Epoch {epoch+1}: Train Loss={avg_train_loss:.4f}, Train IoU={avg_train_iou:.4f}, Val Loss={avg_val_loss:.4f}, Val IoU={avg_val_iou:.4f}, LR={current_lr:.6f}")

        # Save best model
        if avg_val_iou > best_iou:
            best_iou = avg_val_iou
            torch.save(model.state_dict(), f'best_optimal_cswin_{image_type}.pth')
            print(f" New best IoU: {best_iou:.4f}")

        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_iou': best_iou,
                'train_losses': train_losses,
                'val_losses': val_losses,
                'train_ious': train_ious,
                'val_ious': val_ious,
                'learning_rates': learning_rates
            }, f'checkpoint_epoch_{epoch+1}_{image_type}.pth')

        # Early stopping if target reached (only if enabled)
        if not no_early_stop and avg_val_iou >= target_iou:
            print(f" Target IoU {target_iou} achieved! Best IoU: {best_iou:.4f}")
            print(f"Early stopping at epoch {epoch+1}")
            break

    # Generate comprehensive results
    print("\n" + "="*60)
    print(" GENERATING COMPREHENSIVE RESULTS")
    print("="*60)

    # Generate predictions for ALL samples
    generate_all_predictions(model, dataset, device, image_type)

    # Plot comprehensive training curves
    plot_comprehensive_curves(train_losses, val_losses, train_ious, val_ious, learning_rates, image_type)

    # Generate detailed metrics report
    generate_metrics_report(train_losses, val_losses, train_ious, val_ious, best_iou, image_type, epochs)

    # Create beautiful prediction gallery
    create_prediction_gallery(image_type)

    return best_iou

def generate_all_predictions(model, dataset, device, image_type):
    """Generate predictions for ALL samples with beautiful visualization"""
    model.eval()

    # Create directories
    pred_dir = f'predictions_{image_type}_all'
    os.makedirs(pred_dir, exist_ok=True)
    os.makedirs(f'{pred_dir}/individual', exist_ok=True)
    os.makedirs(f'{pred_dir}/overlays', exist_ok=True)

    print(f"📸 Generating predictions for all {len(dataset)} samples...")

    all_ious = []
    all_predictions = []

    with torch.no_grad():
        for idx in tqdm(range(len(dataset)), desc="Generating predictions"):
            sample = dataset[idx]
            image = sample['image'].unsqueeze(0).to(device)
            label = sample['label'].unsqueeze(0).to(device)
            case_name = sample['case_name']

            # Get prediction
            output = model(image)
            prediction = torch.argmax(output, dim=1)
            iou = calculate_iou(output, label)

            # Convert to numpy
            img_rgb = image[0, :3].cpu().numpy().transpose(1, 2, 0)
            img_ir = image[0, 3].cpu().numpy()
            label_np = label[0].cpu().numpy()
            pred_np = prediction[0].cpu().numpy()

            # Normalize for display
            img_rgb = np.clip(img_rgb, 0, 1)

            # Store results
            all_ious.append(iou)
            all_predictions.append({
                'case_name': case_name,
                'iou': iou,
                'image_rgb': img_rgb,
                'image_ir': img_ir,
                'label': label_np,
                'prediction': pred_np
            })

            # Create individual prediction visualization
            create_individual_prediction(img_rgb, img_ir, label_np, pred_np, case_name, iou,
                                       f'{pred_dir}/individual/{case_name}.png')

            # Create overlay visualization
            create_overlay_prediction(img_rgb, label_np, pred_np, case_name, iou,
                                    f'{pred_dir}/overlays/{case_name}_overlay.png')

    # Generate summary statistics
    print(f"\n Prediction Summary:")
    print(f"Total samples: {len(all_ious)}")
    print(f"Mean IoU: {np.mean(all_ious):.4f}")
    print(f"Std IoU: {np.std(all_ious):.4f}")
    print(f"Min IoU: {np.min(all_ious):.4f}")
    print(f"Max IoU: {np.max(all_ious):.4f}")
    print(f"Samples with IoU > 0.8: {sum(1 for iou in all_ious if iou > 0.8)}/{len(all_ious)}")
    print(f"Samples with IoU > 0.85: {sum(1 for iou in all_ious if iou > 0.85)}/{len(all_ious)}")

    return all_predictions, all_ious

def create_individual_prediction(img_rgb, img_ir, label, pred, case_name, iou, save_path):
    """Create beautiful individual prediction visualization"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # Top row: RGB, IR, Combined
    axes[0, 0].imshow(img_rgb)
    axes[0, 0].set_title('RGB Image', fontsize=14, fontweight='bold')
    axes[0, 0].axis('off')

    axes[0, 1].imshow(img_ir, cmap='hot')
    axes[0, 1].set_title('IR Image', fontsize=14, fontweight='bold')
    axes[0, 1].axis('off')

    # Create RGB+IR overlay
    rgb_gray = np.mean(img_rgb, axis=2)
    combined = np.stack([rgb_gray, img_ir, rgb_gray], axis=2)
    combined = np.clip(combined, 0, 1)
    axes[0, 2].imshow(combined)
    axes[0, 2].set_title('RGB+IR Combined', fontsize=14, fontweight='bold')
    axes[0, 2].axis('off')

    # Bottom row: Ground Truth, Prediction, Difference
    axes[1, 0].imshow(label, cmap='Blues', alpha=0.8)
    axes[1, 0].set_title('Ground Truth', fontsize=14, fontweight='bold')
    axes[1, 0].axis('off')

    axes[1, 1].imshow(pred, cmap='Reds', alpha=0.8)
    axes[1, 1].set_title('Prediction', fontsize=14, fontweight='bold')
    axes[1, 1].axis('off')

    # Difference map
    diff = np.abs(label.astype(float) - pred.astype(float))
    axes[1, 2].imshow(diff, cmap='RdYlBu_r')
    axes[1, 2].set_title('Difference Map', fontsize=14, fontweight='bold')
    axes[1, 2].axis('off')

    # Add colorbar for difference
    cbar = plt.colorbar(axes[1, 2].images[0], ax=axes[1, 2], fraction=0.046, pad=0.04)
    cbar.set_label('Absolute Difference', fontsize=12)

    # Overall title
    color = 'green' if iou > 0.8 else 'orange' if iou > 0.6 else 'red'
    plt.suptitle(f'{case_name} - IoU: {iou:.4f}', fontsize=16, fontweight='bold', color=color)

    plt.tight_layout()
    plt.savefig(save_path, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close()

def create_overlay_prediction(img_rgb, label, pred, case_name, iou, save_path):
    """Create overlay visualization with transparency"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    # Original image
    axes[0].imshow(img_rgb)
    axes[0].set_title('Original Image', fontsize=14, fontweight='bold')
    axes[0].axis('off')

    # Ground truth overlay
    axes[1].imshow(img_rgb)
    axes[1].imshow(label, cmap='Blues', alpha=0.5)
    axes[1].set_title('Ground Truth Overlay', fontsize=14, fontweight='bold')
    axes[1].axis('off')

    # Prediction overlay
    axes[2].imshow(img_rgb)
    axes[2].imshow(pred, cmap='Reds', alpha=0.5)
    axes[2].set_title('Prediction Overlay', fontsize=14, fontweight='bold')
    axes[2].axis('off')

    # Overall title
    color = 'green' if iou > 0.8 else 'orange' if iou > 0.6 else 'red'
    plt.suptitle(f'{case_name} - IoU: {iou:.4f}', fontsize=16, fontweight='bold', color=color)

    plt.tight_layout()
    plt.savefig(save_path, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close()

def plot_comprehensive_curves(train_losses, val_losses, train_ious, val_ious, learning_rates, image_type):
    """Plot comprehensive training curves with multiple metrics"""
    fig = plt.figure(figsize=(20, 15))

    # Create a 3x2 subplot layout
    gs = fig.add_gridspec(3, 2, hspace=0.3, wspace=0.3)

    # 1. Loss curves
    ax1 = fig.add_subplot(gs[0, 0])
    epochs = range(1, len(train_losses) + 1)
    ax1.plot(epochs, train_losses, label='Train Loss', color='blue', linewidth=2)
    ax1.plot(epochs, val_losses, label='Val Loss', color='red', linewidth=2)
    ax1.set_xlabel('Epoch', fontsize=12)
    ax1.set_ylabel('Loss', fontsize=12)
    ax1.set_title(f'{image_type.upper()} - Loss Curves', fontsize=14, fontweight='bold')
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(1, len(train_losses))

    # 2. IoU curves
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.plot(epochs, train_ious, label='Train IoU', color='green', linewidth=2)
    ax2.plot(epochs, val_ious, label='Val IoU', color='orange', linewidth=2)
    ax2.axhline(y=0.85, color='red', linestyle='--', linewidth=2, label='Target IoU (0.85)')
    ax2.set_xlabel('Epoch', fontsize=12)
    ax2.set_ylabel('IoU', fontsize=12)
    ax2.set_title(f'{image_type.upper()} - IoU Progress', fontsize=14, fontweight='bold')
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(1, len(train_losses))
    ax2.set_ylim(0, 1)

    # 3. Learning rate
    ax3 = fig.add_subplot(gs[1, 0])
    ax3.plot(epochs, learning_rates, color='purple', linewidth=2)
    ax3.set_xlabel('Epoch', fontsize=12)
    ax3.set_ylabel('Learning Rate', fontsize=12)
    ax3.set_title('Learning Rate Schedule', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3)
    ax3.set_xlim(1, len(train_losses))
    ax3.set_yscale('log')

    # 4. Loss difference (overfitting indicator)
    ax4 = fig.add_subplot(gs[1, 1])
    loss_diff = [val - train for val, train in zip(val_losses, train_losses)]
    ax4.plot(epochs, loss_diff, color='red', linewidth=2)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax4.set_xlabel('Epoch', fontsize=12)
    ax4.set_ylabel('Val Loss - Train Loss', fontsize=12)
    ax4.set_title('Overfitting Indicator', fontsize=14, fontweight='bold')
    ax4.grid(True, alpha=0.3)
    ax4.set_xlim(1, len(train_losses))

    # 5. IoU improvement rate
    ax5 = fig.add_subplot(gs[2, 0])
    iou_improvement = [0] + [val_ious[i] - val_ious[i-1] for i in range(1, len(val_ious))]
    ax5.plot(epochs, iou_improvement, color='green', linewidth=2)
    ax5.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax5.set_xlabel('Epoch', fontsize=12)
    ax5.set_ylabel('IoU Improvement', fontsize=12)
    ax5.set_title('IoU Improvement Rate', fontsize=14, fontweight='bold')
    ax5.grid(True, alpha=0.3)
    ax5.set_xlim(1, len(train_losses))

    # 6. Combined metrics
    ax6 = fig.add_subplot(gs[2, 1])
    # Normalize metrics to 0-1 for comparison
    norm_train_loss = [(max(train_losses) - loss) / (max(train_losses) - min(train_losses)) for loss in train_losses]
    norm_val_loss = [(max(val_losses) - loss) / (max(val_losses) - min(val_losses)) for loss in val_losses]

    ax6.plot(epochs, norm_train_loss, label='Norm Train Loss (inv)', alpha=0.7)
    ax6.plot(epochs, norm_val_loss, label='Norm Val Loss (inv)', alpha=0.7)
    ax6.plot(epochs, train_ious, label='Train IoU', alpha=0.7)
    ax6.plot(epochs, val_ious, label='Val IoU', alpha=0.7)
    ax6.set_xlabel('Epoch', fontsize=12)
    ax6.set_ylabel('Normalized Metrics', fontsize=12)
    ax6.set_title('All Metrics Comparison', fontsize=14, fontweight='bold')
    ax6.legend(fontsize=10)
    ax6.grid(True, alpha=0.3)
    ax6.set_xlim(1, len(train_losses))
    ax6.set_ylim(0, 1)

    plt.suptitle(f'{image_type.upper()} Dataset - Comprehensive Training Analysis',
                 fontsize=16, fontweight='bold', y=0.98)

    plt.savefig(f'comprehensive_training_curves_{image_type}.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    # Also create a simple version for quick viewing
    plot_simple_curves(train_losses, val_losses, train_ious, val_ious, image_type)

def plot_simple_curves(train_losses, val_losses, train_ious, val_ious, image_type):
    """Plot simple training curves for quick viewing"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    epochs = range(1, len(train_losses) + 1)

    # Loss curves
    ax1.plot(epochs, train_losses, label='Train Loss', linewidth=2)
    ax1.plot(epochs, val_losses, label='Val Loss', linewidth=2)
    ax1.set_xlabel('Epoch', fontsize=12)
    ax1.set_ylabel('Loss', fontsize=12)
    ax1.set_title(f'{image_type.upper()} - Loss Curves', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # IoU curves
    ax2.plot(epochs, train_ious, label='Train IoU', linewidth=2)
    ax2.plot(epochs, val_ious, label='Val IoU', linewidth=2)
    ax2.axhline(y=0.85, color='red', linestyle='--', linewidth=2, label='Target IoU (0.85)')
    ax2.set_xlabel('Epoch', fontsize=12)
    ax2.set_ylabel('IoU', fontsize=12)
    ax2.set_title(f'{image_type.upper()} - IoU Progress', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 1)

    plt.tight_layout()
    plt.savefig(f'simple_training_curves_{image_type}.png', dpi=200, bbox_inches='tight', facecolor='white')
    plt.close()

def generate_metrics_report(train_losses, val_losses, train_ious, val_ious, best_iou, image_type, total_epochs):
    """Generate comprehensive metrics report"""

    report_path = f'metrics_report_{image_type}.txt'

    # Calculate statistics
    final_train_loss = train_losses[-1]
    final_val_loss = val_losses[-1]
    final_train_iou = train_ious[-1]
    final_val_iou = val_ious[-1]

    min_train_loss = min(train_losses)
    min_val_loss = min(val_losses)
    max_train_iou = max(train_ious)
    max_val_iou = max(val_ious)

    # Find epochs of best performance
    best_train_loss_epoch = train_losses.index(min_train_loss) + 1
    best_val_loss_epoch = val_losses.index(min_val_loss) + 1
    best_train_iou_epoch = train_ious.index(max_train_iou) + 1
    best_val_iou_epoch = val_ious.index(max_val_iou) + 1

    # Calculate improvement rates
    train_loss_improvement = (train_losses[0] - final_train_loss) / train_losses[0] * 100
    val_loss_improvement = (val_losses[0] - final_val_loss) / val_losses[0] * 100
    train_iou_improvement = (final_train_iou - train_ious[0]) / train_ious[0] * 100
    val_iou_improvement = (final_val_iou - val_ious[0]) / val_ious[0] * 100

    # Check for overfitting
    final_loss_gap = final_val_loss - final_train_loss
    final_iou_gap = final_train_iou - final_val_iou

    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("="*80 + "\n")
        f.write(f"OPTIMAL CSWIN-UNET TRAINING REPORT - {image_type.upper()} DATASET\n")
        f.write("="*80 + "\n\n")

        f.write(" TRAINING SUMMARY\n")
        f.write("-"*40 + "\n")
        f.write(f"Dataset Type: {image_type.upper()}\n")
        f.write(f"Total Epochs Completed: {len(train_losses)}/{total_epochs}\n")
        f.write(f"Best Validation IoU: {best_iou:.6f}\n")
        f.write(f"Target IoU (0.85): {' ACHIEVED' if best_iou >= 0.85 else ' NOT ACHIEVED'}\n\n")

        f.write(" FINAL METRICS\n")
        f.write("-"*40 + "\n")
        f.write(f"Final Training Loss: {final_train_loss:.6f}\n")
        f.write(f"Final Validation Loss: {final_val_loss:.6f}\n")
        f.write(f"Final Training IoU: {final_train_iou:.6f}\n")
        f.write(f"Final Validation IoU: {final_val_iou:.6f}\n\n")

        f.write(" BEST PERFORMANCE\n")
        f.write("-"*40 + "\n")
        f.write(f"Best Training Loss: {min_train_loss:.6f} (Epoch {best_train_loss_epoch})\n")
        f.write(f"Best Validation Loss: {min_val_loss:.6f} (Epoch {best_val_loss_epoch})\n")
        f.write(f"Best Training IoU: {max_train_iou:.6f} (Epoch {best_train_iou_epoch})\n")
        f.write(f"Best Validation IoU: {max_val_iou:.6f} (Epoch {best_val_iou_epoch})\n\n")

        f.write(" IMPROVEMENT ANALYSIS\n")
        f.write("-"*40 + "\n")
        f.write(f"Training Loss Improvement: {train_loss_improvement:.2f}%\n")
        f.write(f"Validation Loss Improvement: {val_loss_improvement:.2f}%\n")
        f.write(f"Training IoU Improvement: {train_iou_improvement:.2f}%\n")
        f.write(f"Validation IoU Improvement: {val_iou_improvement:.2f}%\n\n")

        f.write(" OVERFITTING ANALYSIS\n")
        f.write("-"*40 + "\n")
        f.write(f"Final Loss Gap (Val - Train): {final_loss_gap:.6f}\n")
        f.write(f"Final IoU Gap (Train - Val): {final_iou_gap:.6f}\n")

        if final_loss_gap > 0.1:
            f.write("  WARNING: Significant loss gap detected - possible overfitting\n")
        elif final_loss_gap < 0:
            f.write("ℹ  INFO: Validation loss lower than training - good generalization\n")
        else:
            f.write(" GOOD: Minimal loss gap - balanced training\n")

        if final_iou_gap > 0.05:
            f.write("  WARNING: Training IoU significantly higher than validation\n")
        else:
            f.write(" GOOD: Training and validation IoU are well balanced\n")

        f.write("\n")

        # IoU milestones
        f.write(" IoU MILESTONES\n")
        f.write("-"*40 + "\n")
        milestones = [0.5, 0.6, 0.7, 0.8, 0.85, 0.9]
        for milestone in milestones:
            epochs_above = [i+1 for i, iou in enumerate(val_ious) if iou >= milestone]
            if epochs_above:
                f.write(f"IoU ≥ {milestone}: First achieved at epoch {epochs_above[0]}\n")
            else:
                f.write(f"IoU ≥ {milestone}: Not achieved\n")

        f.write("\n")

        # Training stability
        f.write(" TRAINING STABILITY\n")
        f.write("-"*40 + "\n")

        # Calculate variance in last 10 epochs
        if len(val_ious) >= 10:
            last_10_ious = val_ious[-10:]
            iou_variance = np.var(last_10_ious)
            iou_std = np.std(last_10_ious)
            f.write(f"IoU Variance (last 10 epochs): {iou_variance:.6f}\n")
            f.write(f"IoU Standard Deviation (last 10 epochs): {iou_std:.6f}\n")

            if iou_std < 0.01:
                f.write(" EXCELLENT: Very stable training in final epochs\n")
            elif iou_std < 0.02:
                f.write(" GOOD: Stable training in final epochs\n")
            else:
                f.write("  WARNING: Unstable training in final epochs\n")

        f.write("\n")
        f.write("="*80 + "\n")
        f.write("Report generated by Optimal CSWin-UNet Training Pipeline\n")
        f.write("="*80 + "\n")

    print(f" Detailed metrics report saved to: {report_path}")

def create_prediction_gallery(image_type):
    """Create a beautiful prediction gallery from all predictions"""
    pred_dir = f'predictions_{image_type}_all'
    individual_dir = f'{pred_dir}/individual'

    if not os.path.exists(individual_dir):
        print("  No predictions found to create gallery")
        return

    # Get all prediction files
    pred_files = glob.glob(f'{individual_dir}/*.png')
    pred_files.sort()

    if len(pred_files) == 0:
        print("  No prediction files found")
        return

    print(f" Creating prediction gallery from {len(pred_files)} predictions...")

    # Create gallery with best and worst predictions
    # Load IoU scores from filenames or calculate them
    pred_info = []
    for file_path in pred_files:
        # Extract case name from filename
        case_name = os.path.splitext(os.path.basename(file_path))[0]
        pred_info.append({'file': file_path, 'case': case_name})

    # Create different galleries
    create_best_worst_gallery(pred_info, image_type)
    create_grid_gallery(pred_files[:16], f'sample_gallery_{image_type}.png', 4, 4)  # 4x4 grid of first 16

    print(f" Prediction galleries created successfully!")

def create_best_worst_gallery(pred_info, image_type):
    """Create gallery showing best and worst predictions"""
    # For now, just create a sample gallery since we don't have IoU in filenames
    # In a real implementation, you'd sort by IoU scores

    sample_files = [info['file'] for info in pred_info[:8]]  # First 8 as sample
    create_grid_gallery(sample_files, f'best_predictions_gallery_{image_type}.png', 2, 4)

def create_grid_gallery(image_files, output_path, rows, cols):
    """Create a grid gallery from image files"""
    if len(image_files) == 0:
        return

    fig, axes = plt.subplots(rows, cols, figsize=(cols*6, rows*6))
    if rows == 1:
        axes = axes.reshape(1, -1)
    elif cols == 1:
        axes = axes.reshape(-1, 1)

    for i in range(rows):
        for j in range(cols):
            idx = i * cols + j
            if idx < len(image_files):
                img = plt.imread(image_files[idx])
                axes[i, j].imshow(img)
                axes[i, j].axis('off')

                # Extract case name from filename
                case_name = os.path.splitext(os.path.basename(image_files[idx]))[0]
                axes[i, j].set_title(case_name, fontsize=10)
            else:
                axes[i, j].axis('off')

    plt.tight_layout()
    plt.savefig(output_path, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close()

    print(f" Gallery saved: {output_path}")