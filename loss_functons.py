import torch.nn as nn
import torch.nn.functional as F
import torch
import numpy as np


class CombinedLoss(nn.Module):
    def __init__(self, num_classes=2):
        super(CombinedLoss, self).__init__()
        self.ce_loss = nn.CrossEntropyLoss()
        self.num_classes = num_classes
    
    def dice_loss(self, pred, target):
        smooth = 1e-5
        pred = F.softmax(pred, dim=1)
        target_one_hot = F.one_hot(target, self.num_classes).permute(0, 3, 1, 2).float()
        
        intersection = (pred * target_one_hot).sum(dim=(2, 3))
        union = pred.sum(dim=(2, 3)) + target_one_hot.sum(dim=(2, 3))
        dice = (2 * intersection + smooth) / (union + smooth)
        return 1 - dice.mean()
    
    def iou_loss(self, pred, target):
        smooth = 1e-5
        pred = F.softmax(pred, dim=1)
        target_one_hot = F.one_hot(target, self.num_classes).permute(0, 3, 1, 2).float()
        
        intersection = (pred * target_one_hot).sum(dim=(2, 3))
        union = pred.sum(dim=(2, 3)) + target_one_hot.sum(dim=(2, 3)) - intersection
        iou = (intersection + smooth) / (union + smooth)
        return 1 - iou.mean()
    
    def forward(self, pred, target):
        ce = self.ce_loss(pred, target)
        dice = self.dice_loss(pred, target)
        iou = self.iou_loss(pred, target)
        return 0.3 * ce + 0.4 * dice + 0.3 * iou

def calculate_iou(pred, target, num_classes=2):
    """Calculate IoU metric"""
    pred = torch.argmax(pred, dim=1)
    ious = []
    
    for cls in range(num_classes):
        pred_cls = (pred == cls)
        target_cls = (target == cls)
        intersection = (pred_cls & target_cls).float().sum()
        union = (pred_cls | target_cls).float().sum()
        
        if union == 0:
            ious.append(1.0 if intersection == 0 else 0.0)
        else:
            ious.append((intersection / union).item())
    
    return np.mean(ious)