#!/usr/bin/env python3
"""
Simple launcher script for multi-model training
"""

import os
import sys
import subprocess
from datetime import datetime


def print_banner():
    """Print welcome banner"""
    print("="*80)
    print("🤖 MULTI-MODEL TRAINING PIPELINE")
    print("   Training 5 models with RGB and IR datasets")
    print("="*80)
    print()


def check_requirements():
    """Check if all required files exist"""
    required_files = [
        'multi_model_trainer.py',
        'train_all_models.py',
        'test_models.py',
        'optimal_dataset.py',
        'loss_functons.py'
    ]
    
    required_dirs = [
        'layer',
        'data-if'
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
    
    if missing_files or missing_dirs:
        print("❌ Missing required files/directories:")
        for file in missing_files:
            print(f"   - {file}")
        for dir_name in missing_dirs:
            print(f"   - {dir_name}/")
        return False
    
    return True


def run_tests():
    """Run model and dataset tests"""
    print("🧪 Running tests...")
    try:
        result = subprocess.run([sys.executable, 'test_models.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
            return True
        else:
            print("❌ Tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def get_user_choice():
    """Get user's training choice"""
    print("\n🎯 Training Options:")
    print("1. Quick test (5 epochs, fast)")
    print("2. Standard training (50 epochs)")
    print("3. Extended training (100 epochs)")
    print("4. Custom parameters")
    print("5. Run tests only")
    print("0. Exit")
    
    while True:
        try:
            choice = input("\nSelect option (0-5): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5']:
                return choice
            else:
                print("Please enter a number between 0-5")
        except KeyboardInterrupt:
            print("\n\nExiting...")
            return '0'


def run_training(option):
    """Run training based on user option"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = f"results_{timestamp}"
    
    if option == '1':
        # Quick test
        cmd = [sys.executable, 'train_all_models.py', 
               '--quick_test', '--results_dir', results_dir]
        print(f"🚀 Running quick test (results in {results_dir})")
        
    elif option == '2':
        # Standard training
        cmd = [sys.executable, 'train_all_models.py', 
               '--epochs', '50', '--results_dir', results_dir]
        print(f"⚙️  Running standard training (results in {results_dir})")
        
    elif option == '3':
        # Extended training
        cmd = [sys.executable, 'train_all_models.py', 
               '--epochs', '100', '--results_dir', results_dir]
        print(f"🔥 Running extended training (results in {results_dir})")
        
    elif option == '4':
        # Custom parameters
        print("\n⚙️  Custom Training Parameters:")
        epochs = input("Epochs per model (default 50): ").strip() or "50"
        batch_size = input("Batch size (default 8): ").strip() or "8"
        lr = input("Learning rate (default 0.001): ").strip() or "0.001"
        
        cmd = [sys.executable, 'train_all_models.py',
               '--epochs', epochs,
               '--batch_size', batch_size,
               '--lr', lr,
               '--results_dir', results_dir]
        print(f"🎛️  Running custom training (results in {results_dir})")
        
    elif option == '5':
        # Tests only
        return run_tests()
    
    # Run the training
    try:
        print(f"\n🏃 Starting training...")
        print(f"Command: {' '.join(cmd)}")
        print("=" * 60)
        
        result = subprocess.run(cmd)
        
        if result.returncode == 0:
            print("\n" + "="*60)
            print("🎉 Training completed successfully!")
            print(f"📁 Results saved to: {results_dir}/")
            print("="*60)
            return True
        else:
            print(f"\n❌ Training failed with exit code {result.returncode}")
            return False
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Training interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Error running training: {e}")
        return False


def main():
    """Main function"""
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Please ensure all required files are present")
        return 1
    
    print("✅ All required files found")
    
    # Get user choice
    choice = get_user_choice()
    
    if choice == '0':
        print("👋 Goodbye!")
        return 0
    
    # Run the selected option
    success = run_training(choice)
    
    if success:
        print("\n🎯 Next steps:")
        print("   - Check the results directory for outputs")
        print("   - View training_report.txt for summary")
        print("   - Check model_comparison.png for visualizations")
        print("   - Use best_*.pth files for inference")
        return 0
    else:
        print("\n💡 Troubleshooting tips:")
        print("   - Run option 5 (tests) to diagnose issues")
        print("   - Check that data-if directory has images")
        print("   - Try reducing batch size if out of memory")
        print("   - Ensure CUDA is available for GPU training")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
