try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("Warning: OpenCV not available. Using PIL only.")

from PIL import Image
import glob
import os
import numpy as np
import torch
import torchvision.transforms as transforms


class UnifiedDataset(torch.utils.data.Dataset):
    """
    Unified dataset that can handle both RGB and IR images for all models
    Supports different input channel configurations for different models
    """
    def __init__(self, data_dir, image_type='rgb', transform=None, img_size=224, input_channels=4):
        self.data_dir = data_dir
        self.image_type = image_type
        self.transform = transform
        self.img_size = img_size
        self.input_channels = input_channels  # 3 for RGB-only, 1 for IR-only, 4 for RGB+IR

        # Get image paths
        if image_type == 'rgb':
            self.image_dir = os.path.join(data_dir, '01-Visible images')
        elif image_type == 'ir':
            self.image_dir = os.path.join(data_dir, '02-Infrared images')
        else:  # 'both' - use RGB directory as primary
            self.image_dir = os.path.join(data_dir, '01-Visible images')
            self.ir_dir = os.path.join(data_dir, '02-Infrared images')

        self.label_dir = os.path.join(data_dir, '04-Ground truth')

        # Find all valid image-label pairs
        self.samples = []
        for ext in ['*.png', '*.jpg', '*.jpeg']:
            for img_path in glob.glob(os.path.join(self.image_dir, ext)):
                img_name = os.path.splitext(os.path.basename(img_path))[0]
                label_path = os.path.join(self.label_dir, img_name + '.jpg')

                # For 'both' type, also check if IR image exists
                if image_type == 'both':
                    ir_path = os.path.join(self.ir_dir, os.path.basename(img_path))
                    if os.path.exists(label_path) and os.path.exists(ir_path):
                        self.samples.append((img_path, ir_path, label_path))
                else:
                    if os.path.exists(label_path):
                        self.samples.append((img_path, label_path))

        print(f"Found {len(self.samples)} valid {image_type.upper()} samples")
        print(f"Input channels: {input_channels}")

    def __len__(self):
        return len(self.samples)

    def load_and_preprocess_image(self, img_path, is_ir=False):
        """Load and preprocess image"""
        if CV2_AVAILABLE:
            # Use OpenCV if available
            if is_ir:
                # Load IR image as grayscale
                image = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
                if image is None:
                    image = np.array(Image.open(img_path).convert('L'))
                # Keep as single channel
                image = image[:, :, np.newaxis] if len(image.shape) == 2 else image
            else:
                # Load RGB image
                image = cv2.imread(img_path)
                if image is None:
                    image = np.array(Image.open(img_path).convert('RGB'))
                else:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # Resize
            if len(image.shape) == 2:
                image = cv2.resize(image, (self.img_size, self.img_size))
                image = image[:, :, np.newaxis]
            else:
                image = cv2.resize(image, (self.img_size, self.img_size))
        else:
            # Use PIL only
            if is_ir:
                # Load IR image as grayscale
                image = Image.open(img_path).convert('L')
                image = image.resize((self.img_size, self.img_size))
                image = np.array(image)
                image = image[:, :, np.newaxis]
            else:
                # Load RGB image
                image = Image.open(img_path).convert('RGB')
                image = image.resize((self.img_size, self.img_size))
                image = np.array(image)

        # Ensure proper shape
        if len(image.shape) == 2:
            image = image[:, :, np.newaxis]

        # Normalize
        image = image.astype(np.float32) / 255.0

        return image

    def __getitem__(self, idx):
        if self.image_type == 'both':
            rgb_path, ir_path, label_path = self.samples[idx]

            # Load RGB and IR images
            rgb_image = self.load_and_preprocess_image(rgb_path, is_ir=False)
            ir_image = self.load_and_preprocess_image(ir_path, is_ir=True)

            case_name = os.path.splitext(os.path.basename(rgb_path))[0]
        else:
            img_path, label_path = self.samples[idx]
            case_name = os.path.splitext(os.path.basename(img_path))[0]

            if self.image_type == 'rgb':
                rgb_image = self.load_and_preprocess_image(img_path, is_ir=False)
                # Create fake IR from RGB (grayscale)
                ir_image = np.mean(rgb_image, axis=2, keepdims=True)
            else:  # IR
                ir_image = self.load_and_preprocess_image(img_path, is_ir=True)
                # Create fake RGB from IR
                rgb_image = np.repeat(ir_image, 3, axis=2)

        # Load label
        if CV2_AVAILABLE:
            label = cv2.imread(label_path, cv2.IMREAD_GRAYSCALE)
            if label is None:
                label = np.array(Image.open(label_path).convert('L'))
            label = cv2.resize(label, (self.img_size, self.img_size))
        else:
            label = Image.open(label_path).convert('L')
            label = label.resize((self.img_size, self.img_size))
            label = np.array(label)
        label = (label > 128).astype(np.float32)

        # Create input tensor based on required channels
        if self.input_channels == 1:
            # IR only
            input_tensor = torch.from_numpy(ir_image).permute(2, 0, 1)
        elif self.input_channels == 3:
            # RGB only
            input_tensor = torch.from_numpy(rgb_image).permute(2, 0, 1)
        elif self.input_channels == 4:
            # RGB + IR
            rgb_tensor = torch.from_numpy(rgb_image).permute(2, 0, 1)
            ir_tensor = torch.from_numpy(ir_image).permute(2, 0, 1)
            input_tensor = torch.cat([rgb_tensor, ir_tensor], dim=0)
        else:
            raise ValueError(f"Unsupported input_channels: {self.input_channels}")

        label_tensor = torch.from_numpy(label).long()

        # Apply transforms if provided
        if self.transform:
            # Note: transforms should handle the specific tensor format
            input_tensor = self.transform(input_tensor)

        return {
            'image': input_tensor,
            'label': label_tensor,
            'case_name': case_name
        }


# Keep the original OptimalDataset for backward compatibility
class OptimalDataset(UnifiedDataset):
    def __init__(self, data_dir, image_type='rgb', transform=None, img_size=224):
        super().__init__(data_dir, image_type, transform, img_size, input_channels=4)