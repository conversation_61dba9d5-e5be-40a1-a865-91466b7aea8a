import cv2
from PIL import Image
import glob
import os
import numpy as np
import torch


class OptimalDataset(torch.utils.data.Dataset):
    def __init__(self, data_dir, image_type='rgb', transform=None, img_size=224):
        self.data_dir = data_dir
        self.image_type = image_type
        self.transform = transform
        self.img_size = img_size
        
        # Get image paths
        if image_type == 'rgb':
            self.image_dir = os.path.join(data_dir, '01-Visible images')
        else:
            self.image_dir = os.path.join(data_dir, '02-Infrared images')
        
        self.label_dir = os.path.join(data_dir, '04-Ground truth')
        
        # Find all valid image-label pairs
        self.samples = []
        for ext in ['*.png', '*.jpg', '*.jpeg']:
            for img_path in glob.glob(os.path.join(self.image_dir, ext)):
                img_name = os.path.splitext(os.path.basename(img_path))[0]
                label_path = os.path.join(self.label_dir, img_name + '.jpg')
                if os.path.exists(label_path):
                    self.samples.append((img_path, label_path))
        
        print(f"Found {len(self.samples)} valid {image_type.upper()} samples")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, label_path = self.samples[idx]

        # Load image based on type
        if self.image_type == 'rgb':
            image = cv2.imread(img_path)
            if image is None:
                image = np.array(Image.open(img_path).convert('RGB'))
            else:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        else:  # IR
            # Load IR image as grayscale
            image = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            if image is None:
                image = np.array(Image.open(img_path).convert('L'))
            # Convert to 3-channel for consistency
            image = np.stack([image, image, image], axis=2)

        # Load label
        label = cv2.imread(label_path, cv2.IMREAD_GRAYSCALE)
        if label is None:
            label = np.array(Image.open(label_path).convert('L'))

        # Resize
        image = cv2.resize(image, (self.img_size, self.img_size))
        label = cv2.resize(label, (self.img_size, self.img_size))

        # Normalize
        image = image.astype(np.float32) / 255.0
        label = (label > 128).astype(np.float32)

        # Convert to tensor
        image = torch.from_numpy(image).permute(2, 0, 1)
        label = torch.from_numpy(label).long()

        # Create 4-channel input (RGB + IR)
        if self.image_type == 'rgb':
            ir_channel = torch.mean(image, dim=0, keepdim=True)  # Fake IR from RGB
            input_tensor = torch.cat([image, ir_channel], dim=0)
        else:  # IR
            # For IR: use first channel as IR, and create fake RGB
            ir_channel = image[0:1]  # Use first channel as IR
            rgb_channels = image  # Use all 3 channels as RGB (they're the same)
            input_tensor = torch.cat([rgb_channels, ir_channel], dim=0)

        return {
            'image': input_tensor,
            'label': label,
            'case_name': os.path.splitext(os.path.basename(img_path))[0]
        }