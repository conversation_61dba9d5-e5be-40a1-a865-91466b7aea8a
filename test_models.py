#!/usr/bin/env python3
"""
Test script to verify all models and dataset work correctly
"""

import torch
import numpy as np
from multi_model_trainer import ModelAdapter
from optimal_dataset import UnifiedDataset
import os


def test_model_creation():
    """Test that all models can be created successfully"""
    print("🧪 Testing model creation...")
    
    models_to_test = ['UNet', 'ResUNet']  # CSWin models have compatibility issues
    input_channels = [1, 3, 4]  # Test different input channel configurations
    
    results = {}
    
    for model_name in models_to_test:
        results[model_name] = {}
        print(f"\n  Testing {model_name}...")
        
        for channels in input_channels:
            try:
                model = ModelAdapter.get_model(model_name, input_channels=channels, num_classes=2)
                
                # Test forward pass with dummy data
                dummy_input = torch.randn(1, channels, 224, 224)
                with torch.no_grad():
                    output = model(dummy_input)
                
                results[model_name][channels] = {
                    'success': True,
                    'output_shape': output.shape,
                    'parameters': sum(p.numel() for p in model.parameters())
                }
                
                print(f"    ✅ {channels} channels: Output {output.shape}, Params: {results[model_name][channels]['parameters']:,}")
                
            except Exception as e:
                results[model_name][channels] = {
                    'success': False,
                    'error': str(e)
                }
                print(f"    ❌ {channels} channels: {str(e)}")
    
    return results


def test_dataset():
    """Test dataset loading"""
    print("\n🗂️  Testing dataset loading...")
    
    data_dir = './data-if'
    if not os.path.exists(data_dir):
        print(f"❌ Data directory not found: {data_dir}")
        return False
    
    try:
        # Test different configurations
        configs = [
            ('rgb', 3),
            ('ir', 1),
            ('rgb', 4),  # RGB with fake IR
            ('ir', 4),   # IR with fake RGB
        ]
        
        for image_type, channels in configs:
            print(f"\n  Testing {image_type.upper()} dataset with {channels} channels...")
            
            dataset = UnifiedDataset(
                data_dir=data_dir,
                image_type=image_type,
                input_channels=channels,
                img_size=224
            )
            
            if len(dataset) == 0:
                print(f"    ⚠️  No samples found")
                continue
            
            # Test loading a sample
            sample = dataset[0]
            image = sample['image']
            label = sample['label']
            case_name = sample['case_name']
            
            print(f"    ✅ Loaded sample: {case_name}")
            print(f"       Image shape: {image.shape}")
            print(f"       Label shape: {label.shape}")
            print(f"       Image range: [{image.min():.3f}, {image.max():.3f}]")
            print(f"       Label unique values: {torch.unique(label).tolist()}")
            
    except Exception as e:
        print(f"    ❌ Dataset test failed: {str(e)}")
        return False
    
    return True


def test_training_compatibility():
    """Test that models and dataset work together"""
    print("\n🔗 Testing training compatibility...")
    
    data_dir = './data-if'
    if not os.path.exists(data_dir):
        print(f"❌ Data directory not found: {data_dir}")
        return False
    
    try:
        # Create a small dataset
        dataset = UnifiedDataset(
            data_dir=data_dir,
            image_type='rgb',
            input_channels=4,
            img_size=224
        )
        
        if len(dataset) == 0:
            print("❌ No dataset samples available")
            return False
        
        # Test with UNet
        model = ModelAdapter.get_model('UNet', input_channels=4, num_classes=2)
        
        # Test forward pass
        sample = dataset[0]
        image = sample['image'].unsqueeze(0)  # Add batch dimension
        label = sample['label'].unsqueeze(0)
        
        with torch.no_grad():
            output = model(image)
        
        print(f"✅ Training compatibility test passed")
        print(f"   Input shape: {image.shape}")
        print(f"   Output shape: {output.shape}")
        print(f"   Label shape: {label.shape}")
        
        # Test loss calculation
        from loss_functons import CombinedLoss, calculate_iou
        
        criterion = CombinedLoss(num_classes=2)
        loss = criterion(output, label)
        iou = calculate_iou(output, label)
        
        print(f"   Loss: {loss.item():.4f}")
        print(f"   IoU: {iou:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Training compatibility test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("="*60)
    print("MODEL AND DATASET TESTING")
    print("="*60)
    
    # Test model creation
    model_results = test_model_creation()
    
    # Test dataset
    dataset_ok = test_dataset()
    
    # Test training compatibility
    training_ok = test_training_compatibility()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    # Model summary
    print("\n📊 Model Creation Results:")
    for model_name, channels_results in model_results.items():
        success_count = sum(1 for r in channels_results.values() if r['success'])
        total_count = len(channels_results)
        status = "✅" if success_count == total_count else "⚠️" if success_count > 0 else "❌"
        print(f"  {status} {model_name}: {success_count}/{total_count} configurations working")
    
    print(f"\n📁 Dataset Loading: {'✅ OK' if dataset_ok else '❌ Failed'}")
    print(f"🔗 Training Compatibility: {'✅ OK' if training_ok else '❌ Failed'}")
    
    # Overall status
    all_models_ok = all(
        all(r['success'] for r in channels_results.values())
        for channels_results in model_results.values()
    )
    
    overall_ok = all_models_ok and dataset_ok and training_ok
    
    print(f"\n🎯 Overall Status: {'✅ All tests passed!' if overall_ok else '❌ Some tests failed'}")
    
    if overall_ok:
        print("\n🚀 Ready to run comprehensive training!")
        print("   Run: python train_all_models.py")
        print("   Or quick test: python train_all_models.py --quick_test")
    else:
        print("\n⚠️  Please fix the issues above before running training")
    
    return 0 if overall_ok else 1


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)
