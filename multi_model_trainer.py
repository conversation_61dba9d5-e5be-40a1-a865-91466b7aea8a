import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import models
from layer.UNet import UNet
from layer.ResUNet import ResUnet
from layer.CSWinUnet import CSWinTransformer

# Import CSWin-CBAM with special handling for hyphenated filename
import importlib.util
import sys
spec = importlib.util.spec_from_file_location("cswin_cbam", "layer/CSWinUnet-CBAM.py")
cswin_cbam_module = importlib.util.module_from_spec(spec)
sys.modules["cswin_cbam"] = cswin_cbam_module
spec.loader.exec_module(cswin_cbam_module)
CSWinTransformer_CBAM = cswin_cbam_module.CSWinTransformer_CBAM

# Import utilities
from optimal_dataset import UnifiedDataset
from loss_functons import CombinedLoss, calculate_iou


class ModelAdapter:
    """Adapter to handle different model architectures and input requirements"""
    
    @staticmethod
    def get_model(model_name, input_channels=4, num_classes=2, img_size=224):
        """Get model instance with proper configuration"""
        
        if model_name == 'UNet':
            # UNet expects 3 channels, we'll modify it
            model = UNet(n_channels=input_channels, n_classes=num_classes)
            
        elif model_name == 'ResUNet':
            # ResUNet expects channel parameter
            model = ResUnet(channel=input_channels)
            # Modify output layer for proper number of classes
            model.output_layer = nn.Sequential(
                nn.Conv2d(64, num_classes, 1, 1),
                nn.Softmax(dim=1) if num_classes > 1 else nn.Sigmoid()
            )
            
        elif model_name == 'CSWinUNet':
            # CSWin-UNet expects in_chans parameter
            model = CSWinTransformer(
                img_size=img_size,
                in_chans=input_channels,
                num_classes=num_classes,
                embed_dim=64,
                depth=[1, 2, 9, 1],
                split_size=[1, 2, 7, 7],
                num_heads=[2, 4, 8, 16]
            )
            
        elif model_name == 'CSWinUNet_CBAM':
            # CSWin-UNet with CBAM
            model = CSWinTransformer_CBAM(
                img_size=img_size,
                in_chans=input_channels,
                num_classes=num_classes,
                embed_dim=64,
                depth=[1, 2, 9, 1],
                split_size=[1, 2, 7, 7],
                num_heads=[2, 4, 8, 16],
                use_cbam=True
            )
            
        elif model_name == 'TransUNet':
            # TransUNet is TensorFlow-based, we'll skip it for now
            # or implement a PyTorch version
            raise NotImplementedError("TransUNet requires TensorFlow implementation")
            
        else:
            raise ValueError(f"Unknown model: {model_name}")
        
        return model
    
    @staticmethod
    def get_input_channels(model_name, image_type):
        """Get required input channels for each model"""
        if image_type == 'rgb':
            return 3
        elif image_type == 'ir':
            return 1
        elif image_type == 'both':
            return 4
        else:
            # Default based on model capabilities
            if model_name in ['CSWinUNet', 'CSWinUNet_CBAM']:
                return 4  # These can handle multi-channel
            else:
                return 3  # Default to RGB


class MultiModelTrainer:
    """Comprehensive trainer for multiple models"""
    
    def __init__(self, data_dir, results_dir='multi_model_results'):
        self.data_dir = data_dir
        self.results_dir = results_dir
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Create results directory
        os.makedirs(results_dir, exist_ok=True)
        
        # Available models (excluding TransUNet and CSWin models for now due to compatibility issues)
        self.available_models = ['UNet', 'ResUNet']
        # TODO: Fix CSWin models - they have issues with grouped convolutions
        # self.available_models = ['UNet', 'ResUNet', 'CSWinUNet', 'CSWinUNet_CBAM']
        
        print(f"Using device: {self.device}")
        print(f"Results will be saved to: {results_dir}")
    
    def train_single_model(self, model_name, image_type, epochs=50, batch_size=8, lr=0.001):
        """Train a single model with specified configuration"""
        
        print(f"\n{'='*60}")
        print(f"Training {model_name} with {image_type.upper()} data")
        print(f"{'='*60}")
        
        # Get input channels
        input_channels = ModelAdapter.get_input_channels(model_name, image_type)
        
        # Create dataset
        dataset = UnifiedDataset(
            self.data_dir, 
            image_type=image_type, 
            input_channels=input_channels,
            img_size=224
        )
        
        # Split dataset
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)
        
        # Create model
        model = ModelAdapter.get_model(model_name, input_channels=input_channels, num_classes=2)
        model = model.to(self.device)
        
        # Loss and optimizer
        criterion = CombinedLoss(num_classes=2)
        optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        
        # Training tracking
        best_iou = 0.0
        train_losses, val_losses, val_ious = [], [], []
        
        # Training loop
        for epoch in range(epochs):
            # Training phase
            model.train()
            train_loss = 0.0
            
            for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}"):
                images = batch['image'].to(self.device)
                labels = batch['label'].to(self.device)
                
                optimizer.zero_grad()
                outputs = model(images)
                loss = criterion(outputs, labels)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            val_iou = 0.0
            
            with torch.no_grad():
                for batch in val_loader:
                    images = batch['image'].to(self.device)
                    labels = batch['label'].to(self.device)
                    
                    outputs = model(images)
                    loss = criterion(outputs, labels)
                    iou = calculate_iou(outputs, labels)
                    
                    val_loss += loss.item()
                    val_iou += iou
            
            # Calculate averages
            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(val_loader)
            avg_val_iou = val_iou / len(val_loader)
            
            # Store metrics
            train_losses.append(avg_train_loss)
            val_losses.append(avg_val_loss)
            val_ious.append(avg_val_iou)
            
            scheduler.step()
            
            print(f"Epoch {epoch+1}: Train Loss={avg_train_loss:.4f}, Val Loss={avg_val_loss:.4f}, Val IoU={avg_val_iou:.4f}")
            
            # Save best model
            if avg_val_iou > best_iou:
                best_iou = avg_val_iou
                model_save_path = os.path.join(self.results_dir, f'best_{model_name}_{image_type}.pth')
                torch.save(model.state_dict(), model_save_path)
                print(f"  New best IoU: {best_iou:.4f}")
        
        # Save training history
        history = {
            'model_name': model_name,
            'image_type': image_type,
            'best_iou': best_iou,
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_ious': val_ious,
            'epochs': epochs,
            'batch_size': batch_size,
            'learning_rate': lr,
            'input_channels': input_channels
        }
        
        history_path = os.path.join(self.results_dir, f'history_{model_name}_{image_type}.json')
        with open(history_path, 'w') as f:
            json.dump(history, f, indent=2)
        
        return best_iou, history
    
    def train_all_models(self, image_types=['rgb', 'ir'], epochs=50, batch_size=8, lr=0.001):
        """Train all available models with specified image types"""
        
        print(f"\n{'='*80}")
        print(f"COMPREHENSIVE MULTI-MODEL TRAINING")
        print(f"{'='*80}")
        print(f"Models: {self.available_models}")
        print(f"Image types: {image_types}")
        print(f"Epochs per model: {epochs}")
        print(f"Batch size: {batch_size}")
        print(f"Learning rate: {lr}")
        print(f"{'='*80}")
        
        all_results = {}
        
        for model_name in self.available_models:
            for image_type in image_types:
                try:
                    best_iou, history = self.train_single_model(
                        model_name, image_type, epochs, batch_size, lr
                    )
                    
                    key = f"{model_name}_{image_type}"
                    all_results[key] = {
                        'best_iou': best_iou,
                        'model_name': model_name,
                        'image_type': image_type,
                        'history': history
                    }
                    
                    print(f"\n✓ {model_name} ({image_type.upper()}): Best IoU = {best_iou:.4f}")
                    
                except Exception as e:
                    print(f"\n✗ Failed to train {model_name} with {image_type}: {str(e)}")
                    continue
        
        # Save comprehensive results
        self.save_comprehensive_results(all_results)
        self.create_comparison_plots(all_results)

        return all_results

    def save_comprehensive_results(self, all_results):
        """Save comprehensive results summary"""

        # Create summary
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_experiments': len(all_results),
            'device': str(self.device),
            'results': {}
        }

        # Organize results by model and image type
        for key, result in all_results.items():
            model_name = result['model_name']
            image_type = result['image_type']

            if model_name not in summary['results']:
                summary['results'][model_name] = {}

            summary['results'][model_name][image_type] = {
                'best_iou': result['best_iou'],
                'epochs': result['history']['epochs'],
                'input_channels': result['history']['input_channels']
            }

        # Save summary
        summary_path = os.path.join(self.results_dir, 'comprehensive_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

        # Create readable report
        self.create_text_report(summary)

        print(f"\n📊 Comprehensive results saved to: {summary_path}")

    def create_text_report(self, summary):
        """Create a human-readable text report"""

        report_path = os.path.join(self.results_dir, 'training_report.txt')

        with open(report_path, 'w') as f:
            f.write("="*80 + "\n")
            f.write("COMPREHENSIVE MULTI-MODEL TRAINING REPORT\n")
            f.write("="*80 + "\n")
            f.write(f"Generated: {summary['timestamp']}\n")
            f.write(f"Device: {summary['device']}\n")
            f.write(f"Total Experiments: {summary['total_experiments']}\n")
            f.write("="*80 + "\n\n")

            # Results by model
            for model_name, model_results in summary['results'].items():
                f.write(f"MODEL: {model_name}\n")
                f.write("-" * 40 + "\n")

                for image_type, metrics in model_results.items():
                    f.write(f"  {image_type.upper():>3}: IoU = {metrics['best_iou']:.4f} ")
                    f.write(f"(Epochs: {metrics['epochs']}, Channels: {metrics['input_channels']})\n")

                f.write("\n")

            # Best performing models
            f.write("BEST PERFORMING MODELS\n")
            f.write("-" * 40 + "\n")

            best_overall = max(summary['results'].items(),
                             key=lambda x: max(result['best_iou'] for result in x[1].values()))

            f.write(f"Best Overall: {best_overall[0]}\n")

            for image_type in ['rgb', 'ir']:
                best_for_type = None
                best_iou = 0

                for model_name, model_results in summary['results'].items():
                    if image_type in model_results:
                        if model_results[image_type]['best_iou'] > best_iou:
                            best_iou = model_results[image_type]['best_iou']
                            best_for_type = model_name

                if best_for_type:
                    f.write(f"Best for {image_type.upper()}: {best_for_type} (IoU: {best_iou:.4f})\n")

        print(f"📄 Training report saved to: {report_path}")

    def create_comparison_plots(self, all_results):
        """Create comprehensive comparison plots"""

        # Extract data for plotting
        models = []
        rgb_ious = []
        ir_ious = []

        for key, result in all_results.items():
            model_name = result['model_name']
            image_type = result['image_type']
            iou = result['best_iou']

            if model_name not in models:
                models.append(model_name)
                rgb_ious.append(0)
                ir_ious.append(0)

            model_idx = models.index(model_name)
            if image_type == 'rgb':
                rgb_ious[model_idx] = iou
            elif image_type == 'ir':
                ir_ious[model_idx] = iou

        # Create comparison plot
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # Plot 1: Side-by-side comparison
        x = np.arange(len(models))
        width = 0.35

        bars1 = ax1.bar(x - width/2, rgb_ious, width, label='RGB', alpha=0.8, color='blue')
        bars2 = ax1.bar(x + width/2, ir_ious, width, label='IR', alpha=0.8, color='red')

        ax1.set_xlabel('Models')
        ax1.set_ylabel('IoU Score')
        ax1.set_title('Model Performance Comparison: RGB vs IR')
        ax1.set_xticks(x)
        ax1.set_xticklabels(models, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 1.0)

        # Add value labels on bars
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                if height > 0:
                    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                            f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        # Plot 2: Heatmap
        data_matrix = np.array([rgb_ious, ir_ious])
        im = ax2.imshow(data_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)

        ax2.set_xticks(range(len(models)))
        ax2.set_xticklabels(models, rotation=45)
        ax2.set_yticks([0, 1])
        ax2.set_yticklabels(['RGB', 'IR'])
        ax2.set_title('Performance Heatmap')

        # Add text annotations
        for i in range(2):
            for j in range(len(models)):
                value = data_matrix[i, j]
                if value > 0:
                    text = ax2.text(j, i, f'{value:.3f}', ha="center", va="center",
                                  color="white" if value < 0.5 else "black", fontweight='bold')

        plt.colorbar(im, ax=ax2)
        plt.tight_layout()

        # Save plot
        plot_path = os.path.join(self.results_dir, 'model_comparison.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📈 Comparison plots saved to: {plot_path}")

        # Create individual training curves
        self.create_training_curves(all_results)

    def create_training_curves(self, all_results):
        """Create training curves for all models"""

        # Create subplots for all models
        n_models = len(all_results)
        if n_models == 0:
            return

        cols = 2
        rows = (n_models + 1) // 2

        fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
        if rows == 1:
            axes = axes.reshape(1, -1)

        plot_idx = 0
        for key, result in all_results.items():
            if plot_idx >= rows * cols:
                break

            row = plot_idx // cols
            col = plot_idx % cols
            ax = axes[row, col]

            history = result['history']
            model_name = result['model_name']
            image_type = result['image_type']

            epochs = range(1, len(history['train_losses']) + 1)

            # Plot losses
            ax2 = ax.twinx()

            line1 = ax.plot(epochs, history['val_ious'], 'b-', label='Validation IoU', linewidth=2)
            line2 = ax2.plot(epochs, history['train_losses'], 'r--', label='Train Loss', alpha=0.7)
            line3 = ax2.plot(epochs, history['val_losses'], 'orange', linestyle='--', label='Val Loss', alpha=0.7)

            ax.set_xlabel('Epoch')
            ax.set_ylabel('IoU Score', color='b')
            ax2.set_ylabel('Loss', color='r')
            ax.set_title(f'{model_name} - {image_type.upper()}\nBest IoU: {result["best_iou"]:.4f}')

            # Combine legends
            lines = line1 + line2 + line3
            labels = [l.get_label() for l in lines]
            ax.legend(lines, labels, loc='center right')

            ax.grid(True, alpha=0.3)

            plot_idx += 1

        # Hide empty subplots
        for i in range(plot_idx, rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].set_visible(False)

        plt.tight_layout()

        # Save training curves
        curves_path = os.path.join(self.results_dir, 'training_curves.png')
        plt.savefig(curves_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📈 Training curves saved to: {curves_path}")


def main():
    """Main function to run comprehensive training"""
    import argparse

    parser = argparse.ArgumentParser(description='Multi-Model Training Pipeline')
    parser.add_argument('--data_dir', type=str, default='./data-if', help='Data directory')
    parser.add_argument('--results_dir', type=str, default='multi_model_results', help='Results directory')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs per model')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--image_types', nargs='+', default=['rgb', 'ir'],
                       choices=['rgb', 'ir', 'both'], help='Image types to train on')
    parser.add_argument('--models', nargs='+', default=None,
                       help='Specific models to train (default: all available)')

    args = parser.parse_args()

    # Create trainer
    trainer = MultiModelTrainer(args.data_dir, args.results_dir)

    # Override available models if specified
    if args.models:
        trainer.available_models = [m for m in args.models if m in trainer.available_models]
        print(f"Training only specified models: {trainer.available_models}")

    # Run training
    results = trainer.train_all_models(
        image_types=args.image_types,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr
    )

    print(f"\n{'='*80}")
    print("TRAINING COMPLETED!")
    print(f"{'='*80}")
    print(f"Total experiments: {len(results)}")
    print(f"Results saved to: {args.results_dir}")

    # Print summary
    print("\nSUMMARY:")
    for key, result in results.items():
        print(f"  {result['model_name']} ({result['image_type'].upper()}): IoU = {result['best_iou']:.4f}")


if __name__ == "__main__":
    main()
