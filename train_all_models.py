#!/usr/bin/env python3
"""
Comprehensive training script for all 5 models with RGB and IR datasets
"""

import os
import sys
import argparse
from multi_model_trainer import MultiModelTrainer


def main():
    """Main training function"""
    
    print("="*80)
    print("COMPREHENSIVE MULTI-MODEL TRAINING")
    print("Training 5 models with RGB and IR datasets")
    print("="*80)
    
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train all models with RGB and IR datasets')
    parser.add_argument('--data_dir', type=str, default='./data-if', 
                       help='Path to data directory (default: ./data-if)')
    parser.add_argument('--results_dir', type=str, default='multi_model_results',
                       help='Directory to save results (default: multi_model_results)')
    parser.add_argument('--epochs', type=int, default=50,
                       help='Number of epochs per model (default: 50)')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='Batch size (default: 8)')
    parser.add_argument('--lr', type=float, default=0.001,
                       help='Learning rate (default: 0.001)')
    parser.add_argument('--quick_test', action='store_true',
                       help='Run quick test with fewer epochs')
    
    args = parser.parse_args()
    
    # Adjust for quick test
    if args.quick_test:
        args.epochs = 5
        print("🚀 Running quick test mode (5 epochs per model)")
    
    # Verify data directory exists
    if not os.path.exists(args.data_dir):
        print(f"❌ Error: Data directory '{args.data_dir}' not found!")
        print("Please ensure the data-if folder exists with:")
        print("  - 01-Visible images/")
        print("  - 02-Infrared images/")
        print("  - 04-Ground truth/")
        return 1
    
    # Check for required subdirectories
    required_dirs = ['01-Visible images', '02-Infrared images', '04-Ground truth']
    missing_dirs = []
    for dir_name in required_dirs:
        dir_path = os.path.join(args.data_dir, dir_name)
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"❌ Error: Missing required directories in {args.data_dir}:")
        for dir_name in missing_dirs:
            print(f"  - {dir_name}")
        return 1
    
    print(f"✅ Data directory verified: {args.data_dir}")
    print(f"📁 Results will be saved to: {args.results_dir}")
    print(f"⚙️  Training parameters:")
    print(f"   - Epochs per model: {args.epochs}")
    print(f"   - Batch size: {args.batch_size}")
    print(f"   - Learning rate: {args.lr}")
    print()
    
    # Create trainer
    trainer = MultiModelTrainer(args.data_dir, args.results_dir)
    
    print(f"🤖 Available models: {trainer.available_models}")
    print(f"📊 Image types: RGB and IR")
    print(f"🎯 Total experiments: {len(trainer.available_models) * 2}")
    print()
    
    # Run comprehensive training
    try:
        results = trainer.train_all_models(
            image_types=['rgb', 'ir'],
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr=args.lr
        )
        
        print("\n" + "="*80)
        print("🎉 TRAINING COMPLETED SUCCESSFULLY!")
        print("="*80)
        
        # Print final summary
        print("\n📊 FINAL RESULTS SUMMARY:")
        print("-" * 50)
        
        best_overall = None
        best_iou = 0
        
        for key, result in results.items():
            model_name = result['model_name']
            image_type = result['image_type']
            iou = result['best_iou']
            
            status = "🟢" if iou >= 0.8 else "🟡" if iou >= 0.7 else "🔴"
            print(f"{status} {model_name:15} ({image_type.upper():3}): IoU = {iou:.4f}")
            
            if iou > best_iou:
                best_iou = iou
                best_overall = f"{model_name} ({image_type.upper()})"
        
        print("-" * 50)
        print(f"🏆 Best Overall: {best_overall} with IoU = {best_iou:.4f}")
        
        print(f"\n📁 All results saved to: {args.results_dir}/")
        print("   📊 comprehensive_summary.json - Complete results")
        print("   📄 training_report.txt - Human-readable report")
        print("   📈 model_comparison.png - Performance comparison")
        print("   📈 training_curves.png - Training progress curves")
        print("   💾 best_*.pth - Best model weights")
        print("   📋 history_*.json - Training histories")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  Training interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Training failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


def quick_demo():
    """Run a quick demonstration"""
    print("🚀 Running quick demo...")
    
    # Create trainer with default settings
    trainer = MultiModelTrainer('./data-if', 'demo_results')
    
    # Train just one model quickly
    try:
        result = trainer.train_single_model('UNet', 'rgb', epochs=3, batch_size=4)
        print(f"✅ Demo completed! UNet RGB IoU: {result[0]:.4f}")
        print("📁 Demo results saved to: demo_results/")
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")


if __name__ == "__main__":
    # Check if running as demo
    if len(sys.argv) > 1 and sys.argv[1] == '--demo':
        quick_demo()
    else:
        exit_code = main()
        sys.exit(exit_code)
